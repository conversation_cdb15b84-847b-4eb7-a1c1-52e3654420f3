import React, { useState } from 'react';
import { useAuth } from '../../AuthContext';
// Using a placeholder for the logo - you can replace with actual logo path
// import naroopLogo from '../../assets/naroop-logo.svg';
import './UnifiedHeader.css';

const UnifiedHeader = ({
  onSignIn,
  onSignUp,
  onShowAdminDashboard,
  userProfile,
  userRole,
  ADMIN_ROLES,
  isGuestMode = false,
  onExitGuestMode,
  authUser // Add authUser as a prop
}) => {
  const { logout } = useAuth();
  const [showProfileMenu, setShowProfileMenu] = useState(false);

  const handleProfileMenuToggle = () => {
    setShowProfileMenu(!showProfileMenu);
  };

  const handleProfileMenuClose = () => {
    setShowProfileMenu(false);
  };

  // Determine header type based on authentication state
  const getHeaderType = () => {
    if (isGuestMode) return 'guest';
    if (authUser) return 'authenticated';
    return 'landing';
  };

  const headerType = getHeaderType();

  // Debug logging
  console.log('UnifiedHeader Debug:', {
    authUser: !!authUser,
    authUserEmail: authUser?.email,
    isGuestMode,
    headerType,
    userProfile: !!userProfile
  });

  return (
    <header className={`unified-header unified-header--${headerType}`}>
      <div className="unified-header__container">
        {/* Logo Section */}
        <div className="unified-header__brand">
          <div className="unified-header__logo-placeholder">
            N
          </div>
          <div className="unified-header__brand-text">
            <span className="unified-header__logo-text">NAROOP</span>
            <span className="unified-header__tagline">Narrative of Our People</span>
          </div>
        </div>

        {/* Actions Section */}
        <div className="unified-header__actions">
          {headerType === 'landing' && (
            <>
              <button 
                className="unified-header__action-btn unified-header__action-btn--secondary"
                onClick={onSignIn}
                aria-label="Sign in to your account"
              >
                Sign In
              </button>
              <button 
                className="unified-header__action-btn unified-header__action-btn--primary"
                onClick={onSignUp}
                aria-label="Create a new account"
              >
                Sign Up
              </button>
            </>
          )}

          {headerType === 'guest' && (
            <>
              <div className="unified-header__guest-indicator">
                <span className="unified-header__guest-icon">👋</span>
                <span className="unified-header__guest-text">Browsing as Guest</span>
              </div>
              <button
                className="unified-header__action-btn unified-header__action-btn--primary"
                onClick={onExitGuestMode}
                aria-label="Return to landing page to sign up or sign in"
              >
                Join NAROOP
              </button>
            </>
          )}

          {headerType === 'authenticated' && (
            <>
              {/* Notifications Button */}
              <button
                className="unified-header__notification-btn"
                aria-label="View notifications"
              >
                <svg className="unified-header__icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5V3h0z" />
                  <circle cx="12" cy="12" r="3" />
                </svg>
                <span className="unified-header__notification-badge">3</span>
              </button>

              {/* Profile Menu */}
              <div className="unified-header__profile-menu">
                <button 
                  className="unified-header__profile-btn"
                  onClick={handleProfileMenuToggle}
                  aria-label="Open profile menu"
                  aria-expanded={showProfileMenu}
                >
                  <div className="unified-header__profile-avatar">
                    {userProfile?.name?.charAt(0) || authUser?.email?.charAt(0) || 'U'}
                  </div>
                  <span className="unified-header__profile-name">
                    {userProfile?.name || authUser?.email}
                  </span>
                  <svg 
                    className={`unified-header__dropdown-icon ${showProfileMenu ? 'unified-header__dropdown-icon--open' : ''}`}
                    viewBox="0 0 24 24" 
                    fill="none" 
                    stroke="currentColor"
                  >
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </button>

                {showProfileMenu && (
                  <>
                    <div 
                      className="unified-header__menu-overlay"
                      onClick={handleProfileMenuClose}
                    />
                    <div className="unified-header__dropdown-menu">
                      <div className="unified-header__dropdown-header">
                        <div className="unified-header__dropdown-avatar">
                          {userProfile?.name?.charAt(0) || authUser?.email?.charAt(0) || 'U'}
                        </div>
                        <div className="unified-header__dropdown-info">
                          <div className="unified-header__dropdown-name">
                            {userProfile?.name || 'User'}
                          </div>
                          <div className="unified-header__dropdown-email">
                            {authUser?.email}
                          </div>
                        </div>
                      </div>

                      <div className="unified-header__dropdown-divider" />

                      <nav className="unified-header__dropdown-nav">
                        <button className="unified-header__dropdown-item">
                          <svg className="unified-header__dropdown-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                          </svg>
                          Profile Settings
                        </button>

                        <button className="unified-header__dropdown-item">
                          <svg className="unified-header__dropdown-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <circle cx="12" cy="12" r="3"></circle>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                          </svg>
                          Account Settings
                        </button>

                        <button className="unified-header__dropdown-item">
                          <svg className="unified-header__dropdown-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                          </svg>
                          Privacy Settings
                        </button>

                        {userRole && userRole.role !== ADMIN_ROLES?.USER && (
                          <>
                            <div className="unified-header__dropdown-divider" />
                            <button 
                              className="unified-header__dropdown-item unified-header__dropdown-item--admin"
                              onClick={() => {
                                onShowAdminDashboard();
                                handleProfileMenuClose();
                              }}
                            >
                              <svg className="unified-header__dropdown-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                              </svg>
                              Admin Dashboard
                            </button>
                          </>
                        )}

                        <div className="unified-header__dropdown-divider" />

                        <button
                          className="unified-header__dropdown-item unified-header__dropdown-item--danger"
                          onClick={async () => {
                            try {
                              await logout();
                              handleProfileMenuClose();
                            } catch (error) {
                              console.error('Error logging out:', error);
                            }
                          }}
                        >
                          <svg className="unified-header__dropdown-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                            <polyline points="16,17 21,12 16,7"></polyline>
                            <line x1="21" y1="12" x2="9" y2="12"></line>
                          </svg>
                          Sign Out
                        </button>
                      </nav>
                    </div>
                  </>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </header>
  );
};

export default UnifiedHeader;
