/* NAROOP Unified Header - Black & Gold Theme */

.unified-header {
  background: linear-gradient(135deg, var(--color-heritage-black) 0%, var(--color-heritage-deep-gold) 100%);
  color: var(--color-heritage-cream);
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 12px rgba(26, 26, 26, 0.15);
  border-bottom: 2px solid var(--color-heritage-gold);
}

.unified-header__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 64px;
}

/* Brand Section */
.unified-header__brand {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  flex-shrink: 0;
}

.unified-header__logo-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: 2px solid var(--color-heritage-gold);
  background: var(--color-heritage-gold);
  color: var(--color-heritage-black);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.5rem;
}

.unified-header__brand-text {
  display: flex;
  flex-direction: column;
  line-height: 1.2;
}

.unified-header__logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-heritage-gold);
  letter-spacing: 0.05em;
}

.unified-header__tagline {
  font-size: 0.75rem;
  color: var(--color-heritage-cream);
  opacity: 0.9;
  font-style: italic;
}

/* Actions Section */
.unified-header__actions {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  flex-shrink: 0;
}

/* Action Buttons */
.unified-header__action-btn {
  padding: 0.5rem 1rem;
  border-radius: 24px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid;
  min-height: 44px;
  min-width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.unified-header__action-btn--primary {
  background: var(--color-heritage-gold);
  border-color: var(--color-heritage-gold);
  color: var(--color-heritage-black);
}

.unified-header__action-btn--primary:hover {
  background: var(--color-empowerment-amber);
  border-color: var(--color-empowerment-amber);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.unified-header__action-btn--secondary {
  background: transparent;
  border-color: var(--color-heritage-cream);
  color: var(--color-heritage-cream);
}

.unified-header__action-btn--secondary:hover {
  background: var(--color-heritage-cream);
  color: var(--color-heritage-black);
  transform: translateY(-1px);
}

/* Guest Mode Indicator */
.unified-header__guest-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: 0.5rem 0.75rem;
  background: rgba(255, 248, 220, 0.1);
  border-radius: 20px;
  border: 1px solid var(--color-heritage-cream);
}

.unified-header__guest-icon {
  font-size: 1rem;
}

.unified-header__guest-text {
  font-size: 0.875rem;
  color: var(--color-heritage-cream);
  font-weight: 500;
}

/* Notifications */
.unified-header__notification-btn {
  position: relative;
  background: transparent;
  border: 2px solid var(--color-heritage-cream);
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--color-heritage-cream);
}

.unified-header__notification-btn:hover {
  background: var(--color-heritage-cream);
  color: var(--color-heritage-black);
  transform: scale(1.05);
}

.unified-header__icon {
  width: 20px;
  height: 20px;
  stroke-width: 2;
}

.unified-header__notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: var(--color-heritage-gold);
  color: var(--color-heritage-black);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 700;
  border: 2px solid var(--color-heritage-black);
}

/* Profile Menu */
.unified-header__profile-menu {
  position: relative;
}

.unified-header__profile-btn {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  background: transparent;
  border: 2px solid var(--color-heritage-cream);
  border-radius: 24px;
  padding: 0.25rem 0.75rem 0.25rem 0.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--color-heritage-cream);
  min-height: 44px;
}

.unified-header__profile-btn:hover {
  background: rgba(255, 248, 220, 0.1);
  border-color: var(--color-heritage-gold);
}

.unified-header__profile-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--color-heritage-gold);
  color: var(--color-heritage-black);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
}

.unified-header__profile-name {
  font-size: 0.875rem;
  font-weight: 500;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.unified-header__dropdown-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.unified-header__dropdown-icon--open {
  transform: rotate(180deg);
}

/* Dropdown Menu */
.unified-header__menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.unified-header__dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: var(--color-heritage-cream);
  border: 2px solid var(--color-heritage-gold);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(26, 26, 26, 0.2);
  min-width: 280px;
  z-index: 1000;
  overflow: hidden;
}

.unified-header__dropdown-header {
  padding: var(--space-md);
  background: linear-gradient(135deg, var(--color-heritage-gold) 0%, var(--color-empowerment-amber) 100%);
  color: var(--color-heritage-black);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.unified-header__dropdown-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--color-heritage-black);
  color: var(--color-heritage-gold);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.25rem;
}

.unified-header__dropdown-info {
  flex: 1;
}

.unified-header__dropdown-name {
  font-weight: 700;
  font-size: 1rem;
  margin-bottom: 2px;
}

.unified-header__dropdown-email {
  font-size: 0.875rem;
  opacity: 0.8;
}

.unified-header__dropdown-divider {
  height: 1px;
  background: var(--color-heritage-forest);
  margin: 0;
  opacity: 0.3;
}

/* Dropdown Navigation */
.unified-header__dropdown-nav {
  padding: var(--space-xs) 0;
}

.unified-header__dropdown-item {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  background: transparent;
  border: none;
  color: var(--color-heritage-black);
  font-size: 0.875rem;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  min-height: 44px;
}

.unified-header__dropdown-item:hover {
  background: var(--color-prosperity-champagne);
  color: var(--color-heritage-black);
}

.unified-header__dropdown-item--admin {
  color: var(--color-heritage-gold);
  font-weight: 600;
}

.unified-header__dropdown-item--admin:hover {
  background: var(--color-heritage-gold);
  color: var(--color-heritage-black);
}

.unified-header__dropdown-item--danger {
  color: var(--color-heritage-burgundy);
}

.unified-header__dropdown-item--danger:hover {
  background: var(--color-heritage-burgundy);
  color: var(--color-heritage-cream);
}

.unified-header__dropdown-icon {
  width: 18px;
  height: 18px;
  stroke-width: 2;
  flex-shrink: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .unified-header__container {
    padding: 0 var(--space-sm);
    min-height: 56px;
  }

  .unified-header__logo-text {
    font-size: 1.25rem;
  }

  .unified-header__tagline {
    display: none;
  }

  .unified-header__profile-name {
    display: none;
  }

  .unified-header__guest-text {
    display: none;
  }

  .unified-header__action-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    min-width: 70px;
  }

  .unified-header__dropdown-menu {
    right: var(--space-sm);
    left: var(--space-sm);
    min-width: auto;
    width: calc(100vw - 2 * var(--space-sm));
    max-width: calc(100vw - 2 * var(--space-sm));
  }
}

@media (max-width: 480px) {
  .unified-header__actions {
    gap: var(--space-xs);
  }

  .unified-header__action-btn {
    padding: 0.4rem 0.6rem;
    min-width: 60px;
  }

  .unified-header__logo-text {
    font-size: 1.1rem;
  }

  .unified-header__dropdown-menu {
    right: var(--space-xs);
    left: var(--space-xs);
    width: calc(100vw - 2 * var(--space-xs));
    max-width: calc(100vw - 2 * var(--space-xs));
  }

  .unified-header__dropdown-header {
    padding: var(--space-md) var(--space-sm);
  }

  .unified-header__dropdown-item {
    padding: var(--space-md) var(--space-sm);
    font-size: 0.9rem;
  }
}
